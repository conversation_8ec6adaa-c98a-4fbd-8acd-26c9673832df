/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : soft_iic.c
  * @brief          : Software I2C implementation
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "soft_iic.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/**
 * @brief  微秒级延时函数
 * @param  us: 延时微秒数
 * @retval None
 */
static void delay_us(uint32_t us)
{
    uint32_t delay = us * (SystemCoreClock / 1000000) / 4;
    while(delay--);
}

/**
 * @brief  设置SDA为输入模式
 * @param  None
 * @retval None
 */
void IIC_SDA_IN(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = IIC_SDA_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(IIC_SDA_Port, &GPIO_InitStruct);
}

/**
 * @brief  设置SDA为输出模式
 * @param  None
 * @retval None
 */
void IIC_SDA_OUT(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = IIC_SDA_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(IIC_SDA_Port, &GPIO_InitStruct);
}

/**
 * @brief  初始化IIC
 * @param  None
 * @retval None
 */
void IIC_Init(void)
{
    IIC_SDA_OUT();
    IIC_SDA_HIGH();
    IIC_SCL_HIGH();
}

/**
 * @brief  产生IIC起始信号
 * @param  None
 * @retval None
 */
void IIC_Start(void)
{
    IIC_SDA_OUT();
    IIC_SDA_HIGH();
    IIC_SCL_HIGH();
    delay_us(4);
    IIC_SDA_LOW();
    delay_us(4);
    IIC_SCL_LOW();
}

/**
 * @brief  产生IIC停止信号
 * @param  None
 * @retval None
 */
void IIC_Stop(void)
{
    IIC_SDA_OUT();
    IIC_SCL_LOW();
    IIC_SDA_LOW();
    delay_us(4);
    IIC_SCL_HIGH();
    delay_us(4);
    IIC_SDA_HIGH();
    delay_us(4);
}

/**
 * @brief  等待应答信号到来
 * @param  None
 * @retval 1: 接收应答失败
 *         0: 接收应答成功
 */
uint8_t IIC_Wait_Ack(void)
{
    uint8_t ucErrTime = 0;
    IIC_SDA_IN();
    IIC_SDA_HIGH();
    delay_us(1);
    IIC_SCL_HIGH();
    delay_us(1);
    while(IIC_SDA_READ())
    {
        ucErrTime++;
        if(ucErrTime > 250)
        {
            IIC_Stop();
            return 1;
        }
    }
    IIC_SCL_LOW();
    return 0;
}

/**
 * @brief  产生ACK应答
 * @param  None
 * @retval None
 */
void IIC_Ack(void)
{
    IIC_SCL_LOW();
    IIC_SDA_OUT();
    IIC_SDA_LOW();
    delay_us(2);
    IIC_SCL_HIGH();
    delay_us(2);
    IIC_SCL_LOW();
}

/**
 * @brief  不产生ACK应答
 * @param  None
 * @retval None
 */
void IIC_NAck(void)
{
    IIC_SCL_LOW();
    IIC_SDA_OUT();
    IIC_SDA_HIGH();
    delay_us(2);
    IIC_SCL_HIGH();
    delay_us(2);
    IIC_SCL_LOW();
}

/**
 * @brief  IIC发送一个字节
 * @param  txd: 发送一个字节
 * @retval None
 */
void IIC_Send_Byte(uint8_t txd)
{
    uint8_t t;
    IIC_SDA_OUT();
    IIC_SCL_LOW();
    for(t = 0; t < 8; t++)
    {
        if((txd & 0x80) >> 7)
            IIC_SDA_HIGH();
        else
            IIC_SDA_LOW();
        txd <<= 1;
        delay_us(2);
        IIC_SCL_HIGH();
        delay_us(2);
        IIC_SCL_LOW();
        delay_us(2);
    }
}

/**
 * @brief  读1个字节
 * @param  ack: ack=1时，发送ACK，ack=0，发送nACK
 * @retval 接收到的数据
 */
uint8_t IIC_Read_Byte(uint8_t ack)
{
    unsigned char i, receive = 0;
    IIC_SDA_IN();
    for(i = 0; i < 8; i++)
    {
        IIC_SCL_LOW();
        delay_us(2);
        IIC_SCL_HIGH();
        receive <<= 1;
        if(IIC_SDA_READ())
            receive++;
        delay_us(1);
    }
    if(!ack)
        IIC_NAck();
    else
        IIC_Ack();
    return receive;
}

/* USER CODE END 0 */
