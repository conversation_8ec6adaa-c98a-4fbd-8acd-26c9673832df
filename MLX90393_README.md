# MLX90393 磁传感器测试项目

## 项目概述
这是一个基于STM32L031G6U6单片机的MLX90393磁传感器测试项目。项目使用软件I2C方式与传感器通信，并通过串口2输出测试数据。

## 硬件连接
- **单片机**: STM32L031G6U6
- **传感器**: MLX90393
- **连接方式**:
  - PC15 (SCL) ↔ MLX90393 SCL
  - PC14 (SDA) ↔ MLX90393 SDA
  - VCC ↔ 3.3V
  - GND ↔ GND

## 文件说明

### 头文件 (Inc/)
1. **soft_iic.h** - 软件I2C驱动头文件
   - 定义I2C引脚宏
   - 声明I2C基础操作函数

2. **mlx90393.h** - MLX90393传感器驱动头文件
   - 定义传感器命令和寄存器
   - 声明传感器操作函数
   - 定义数据结构

### 源文件 (Src/)
1. **soft_iic.c** - 软件I2C驱动实现
   - 实现I2C时序控制
   - 提供字节读写功能

2. **mlx90393.c** - MLX90393传感器驱动实现
   - 传感器初始化和配置
   - 数据读取和处理
   - 寄存器操作

3. **main.c** - 主程序 (已修改)
   - 传感器初始化
   - 定时数据读取
   - 串口数据输出

## 主要功能

### 1. 传感器初始化
```c
MLX90393_Status_t status = MLX90393_Init();
```

### 2. 读取所有轴数据
```c
MLX90393_Data_t sensor_data;
MLX90393_Status_t status = MLX90393_ReadAllData(&sensor_data);
```

### 3. 数据输出格式
```
MLX90393 Data:
X: 12.34 uT
Y: 56.78 uT
Z: 90.12 uT
Temperature: 25.67 °C
-------------------
磁场强度: 105.23 uT
```

## 程序流程
1. 系统初始化 (GPIO, USART2)
2. MLX90393传感器初始化
3. 主循环:
   - LED闪烁指示系统运行
   - 每1秒读取一次传感器数据
   - 通过串口输出数据
   - 计算并显示磁场强度

## 编译和使用
1. 确保所有文件都已添加到Keil项目中
2. 编译项目
3. 下载到STM32L031G6U6
4. 通过串口调试助手查看输出数据 (波特率根据USART2配置)

## 注意事项
1. 确保I2C引脚连接正确
2. 传感器供电电压为3.3V
3. 串口2已重定向，可直接使用printf输出
4. 如果传感器未找到，检查硬件连接和I2C地址
5. 数据读取间隔可根据需要调整

## 故障排除
- **传感器未找到**: 检查硬件连接和供电
- **数据读取失败**: 检查I2C时序和传感器状态
- **数据异常**: 检查传感器周围是否有强磁场干扰

## API参考

### MLX90393主要函数
- `MLX90393_Init()` - 初始化传感器
- `MLX90393_ReadAllData()` - 读取所有轴数据
- `MLX90393_StartMeasurement()` - 开始测量
- `MLX90393_ReadMeasurement()` - 读取测量结果
- `MLX90393_PrintData()` - 打印数据到串口

### 软件I2C函数
- `IIC_Init()` - 初始化I2C
- `IIC_Start()` - 发送起始信号
- `IIC_Stop()` - 发送停止信号
- `IIC_Send_Byte()` - 发送字节
- `IIC_Read_Byte()` - 读取字节
