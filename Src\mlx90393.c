/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : mlx90393.c
  * @brief          : MLX90393 magnetic sensor driver implementation
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "mlx90393.h"
#include <stdio.h>
#include <math.h>

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN PFP */

static MLX90393_Status_t MLX90393_SendCommand(uint8_t cmd, uint8_t *status);
static MLX90393_Status_t MLX90393_WriteData(uint8_t *data, uint8_t len);
static MLX90393_Status_t MLX90393_ReadData(uint8_t *data, uint8_t len);

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/**
 * @brief  发送命令到MLX90393
 * @param  cmd: 命令字节
 * @param  status: 返回的状态字节
 * @retval MLX90393_Status_t
 */
static MLX90393_Status_t MLX90393_SendCommand(uint8_t cmd, uint8_t *status)
{
    IIC_Start();
    IIC_Send_Byte((MLX90393_I2C_ADDR << 1) | 0);  // 写地址
    if(IIC_Wait_Ack() != 0)
    {
        IIC_Stop();
        return MLX90393_ERROR;
    }

    IIC_Send_Byte(cmd);  // 发送命令
    if(IIC_Wait_Ack() != 0)
    {
        IIC_Stop();
        return MLX90393_ERROR;
    }

    IIC_Stop();
    HAL_Delay(1);  // 等待命令处理

    // 读取状态
    IIC_Start();
    IIC_Send_Byte((MLX90393_I2C_ADDR << 1) | 1);  // 读地址
    if(IIC_Wait_Ack() != 0)
    {
        IIC_Stop();
        return MLX90393_ERROR;
    }

    *status = IIC_Read_Byte(0);  // 读取状态字节
    IIC_Stop();

    return MLX90393_OK;
}

/**
 * @brief  向MLX90393写入数据
 * @param  data: 数据缓冲区
 * @param  len: 数据长度
 * @retval MLX90393_Status_t
 */
static MLX90393_Status_t MLX90393_WriteData(uint8_t *data, uint8_t len)
{
    IIC_Start();
    IIC_Send_Byte((MLX90393_I2C_ADDR << 1) | 0);  // 写地址
    if(IIC_Wait_Ack() != 0)
    {
        IIC_Stop();
        return MLX90393_ERROR;
    }

    for(uint8_t i = 0; i < len; i++)
    {
        IIC_Send_Byte(data[i]);
        if(IIC_Wait_Ack() != 0)
        {
            IIC_Stop();
            return MLX90393_ERROR;
        }
    }

    IIC_Stop();
    return MLX90393_OK;
}

/**
 * @brief  从MLX90393读取数据
 * @param  data: 数据缓冲区
 * @param  len: 数据长度
 * @retval MLX90393_Status_t
 */
static MLX90393_Status_t MLX90393_ReadData(uint8_t *data, uint8_t len)
{
    IIC_Start();
    IIC_Send_Byte((MLX90393_I2C_ADDR << 1) | 1);  // 读地址
    if(IIC_Wait_Ack() != 0)
    {
        IIC_Stop();
        return MLX90393_ERROR;
    }

    for(uint8_t i = 0; i < len; i++)
    {
        if(i == len - 1)
            data[i] = IIC_Read_Byte(0);  // 最后一个字节不发送ACK
        else
            data[i] = IIC_Read_Byte(1);  // 发送ACK
    }

    IIC_Stop();
    return MLX90393_OK;
}

/**
 * @brief  初始化MLX90393传感器
 * @param  None
 * @retval MLX90393_Status_t
 */
MLX90393_Status_t MLX90393_Init(void)
{
    uint8_t status;

    // 初始化I2C
    IIC_Init();
    HAL_Delay(100);  // 等待传感器启动

    // 检查连接
    if(MLX90393_CheckConnection() != MLX90393_OK)
    {
        return MLX90393_NOT_FOUND;
    }

    // 复位传感器
    if(MLX90393_Reset() != MLX90393_OK)
    {
        return MLX90393_ERROR;
    }

    // 退出任何当前模式
    MLX90393_SendCommand(MLX90393_CMD_EXIT, &status);
    HAL_Delay(10);

    // 配置增益 - 使用较高增益提高X轴灵敏度
    if(MLX90393_SetGain(MLX90393_GAIN_5X) != MLX90393_OK)
    {
        return MLX90393_ERROR;
    }
    HAL_Delay(10);

    // 配置分辨率 - 使用高分辨率
    if(MLX90393_SetResolution(MLX90393_RES_19BIT) != MLX90393_OK)
    {
        return MLX90393_ERROR;
    }
    HAL_Delay(10);

    // 配置滤波器 - 使用中等滤波减少噪声
    if(MLX90393_SetFilter(MLX90393_FILTER_2) != MLX90393_OK)
    {
        return MLX90393_ERROR;
    }
    HAL_Delay(10);

    return MLX90393_OK;
}

/**
 * @brief  复位MLX90393传感器
 * @param  None
 * @retval MLX90393_Status_t
 */
MLX90393_Status_t MLX90393_Reset(void)
{
    uint8_t status;

    if(MLX90393_SendCommand(MLX90393_CMD_RESET, &status) != MLX90393_OK)
    {
        return MLX90393_ERROR;
    }

    HAL_Delay(100);  // 等待复位完成

    return MLX90393_OK;
}

/**
 * @brief  检查MLX90393连接
 * @param  None
 * @retval MLX90393_Status_t
 */
MLX90393_Status_t MLX90393_CheckConnection(void)
{
    uint8_t status;

    // 发送NOP命令检查连接
    if(MLX90393_SendCommand(MLX90393_CMD_NOP, &status) != MLX90393_OK)
    {
        return MLX90393_NOT_FOUND;
    }

    return MLX90393_OK;
}

/**
 * @brief  开始测量
 * @param  axis: 要测量的轴 (MLX90393_AXIS_X | MLX90393_AXIS_Y | MLX90393_AXIS_Z | MLX90393_AXIS_T)
 * @retval MLX90393_Status_t
 */
MLX90393_Status_t MLX90393_StartMeasurement(uint8_t axis)
{
    uint8_t status;
    uint8_t cmd = MLX90393_CMD_START_MEASURE | axis;

    if(MLX90393_SendCommand(cmd, &status) != MLX90393_OK)
    {
        return MLX90393_ERROR;
    }

    // 检查状态
    if(status & MLX90393_STATUS_ERROR)
    {
        return MLX90393_ERROR;
    }

    HAL_Delay(10);  // 等待测量完成

    return MLX90393_OK;
}

/**
 * @brief  读取测量数据
 * @param  data: 数据结构指针
 * @param  axis: 读取的轴
 * @retval MLX90393_Status_t
 */
MLX90393_Status_t MLX90393_ReadMeasurement(MLX90393_Data_t *data, uint8_t axis)
{
    uint8_t status;
    uint8_t cmd = MLX90393_CMD_READ_MEASURE | axis;
    uint8_t read_data[8];
    uint8_t data_count = 0;

    // 计算需要读取的数据字节数
    if(axis & MLX90393_AXIS_X) data_count += 2;
    if(axis & MLX90393_AXIS_Y) data_count += 2;
    if(axis & MLX90393_AXIS_Z) data_count += 2;
    if(axis & MLX90393_AXIS_T) data_count += 2;

    // 发送读取命令
    IIC_Start();
    IIC_Send_Byte((MLX90393_I2C_ADDR << 1) | 0);
    if(IIC_Wait_Ack() != 0)
    {
        IIC_Stop();
        return MLX90393_ERROR;
    }

    IIC_Send_Byte(cmd);
    if(IIC_Wait_Ack() != 0)
    {
        IIC_Stop();
        return MLX90393_ERROR;
    }
    IIC_Stop();

    HAL_Delay(1);

    // 读取状态和数据
    IIC_Start();
    IIC_Send_Byte((MLX90393_I2C_ADDR << 1) | 1);
    if(IIC_Wait_Ack() != 0)
    {
        IIC_Stop();
        return MLX90393_ERROR;
    }

    status = IIC_Read_Byte(1);  // 读取状态

    // 检查状态字节
    if(status & MLX90393_STATUS_ERROR)
    {
        IIC_Stop();
        return MLX90393_ERROR;
    }

    for(uint8_t i = 0; i < data_count; i++)
    {
        if(i == data_count - 1)
            read_data[i] = IIC_Read_Byte(0);  // 最后一个字节
        else
            read_data[i] = IIC_Read_Byte(1);
    }
    IIC_Stop();

    // 解析数据
    uint8_t index = 0;
    if(axis & MLX90393_AXIS_X)
    {
        int16_t raw_x = (read_data[index] << 8) | read_data[index + 1];
        data->x = (float)raw_x * 1.5f;  // 转换为uT (默认分辨率1.5uT/LSB)
        index += 2;
    }

    if(axis & MLX90393_AXIS_Y)
    {
        int16_t raw_y = (read_data[index] << 8) | read_data[index + 1];
        data->y = (float)raw_y * 1.5f;  // 转换为uT (默认分辨率1.5uT/LSB)
        index += 2;
    }

    if(axis & MLX90393_AXIS_Z)
    {
        int16_t raw_z = (read_data[index] << 8) | read_data[index + 1];
        data->z = (float)raw_z * 1.5f;  // 转换为uT (默认分辨率1.5uT/LSB)
        index += 2;
    }

    if(axis & MLX90393_AXIS_T)
    {
        int16_t raw_temp = (read_data[index] << 8) | read_data[index + 1];
        data->temp = 25.0f + (float)raw_temp / 45.2f;  // 转换为°C (45.2 LSB/°C)
        index += 2;
    }

    return MLX90393_OK;
}

/**
 * @brief  读取所有轴的数据
 * @param  data: 数据结构指针
 * @retval MLX90393_Status_t
 */
MLX90393_Status_t MLX90393_ReadAllData(MLX90393_Data_t *data)
{
    // 开始测量所有轴
    if(MLX90393_StartMeasurement(MLX90393_AXIS_ALL) != MLX90393_OK)
    {
        return MLX90393_ERROR;
    }

    // 读取所有轴数据
    if(MLX90393_ReadMeasurement(data, MLX90393_AXIS_ALL) != MLX90393_OK)
    {
        return MLX90393_ERROR;
    }

    return MLX90393_OK;
}

/**
 * @brief  写入寄存器
 * @param  reg_addr: 寄存器地址
 * @param  data: 要写入的数据
 * @retval MLX90393_Status_t
 */
MLX90393_Status_t MLX90393_WriteRegister(uint8_t reg_addr, uint16_t data)
{
    uint8_t status;
    uint8_t write_data[3];

    write_data[0] = MLX90393_CMD_WRITE_REG | (reg_addr << 2);
    write_data[1] = (data >> 8) & 0xFF;  // 高字节
    write_data[2] = data & 0xFF;         // 低字节

    if(MLX90393_WriteData(write_data, 3) != MLX90393_OK)
    {
        return MLX90393_ERROR;
    }

    HAL_Delay(1);

    // 读取状态
    if(MLX90393_ReadData(&status, 1) != MLX90393_OK)
    {
        return MLX90393_ERROR;
    }

    if(status & MLX90393_STATUS_ERROR)
    {
        return MLX90393_ERROR;
    }

    return MLX90393_OK;
}

/**
 * @brief  读取寄存器
 * @param  reg_addr: 寄存器地址
 * @param  data: 读取的数据
 * @retval MLX90393_Status_t
 */
MLX90393_Status_t MLX90393_ReadRegister(uint8_t reg_addr, uint16_t *data)
{
    uint8_t status;
    uint8_t cmd = MLX90393_CMD_READ_REG | (reg_addr << 2);
    uint8_t read_data[3];

    if(MLX90393_SendCommand(cmd, &status) != MLX90393_OK)
    {
        return MLX90393_ERROR;
    }

    if(status & MLX90393_STATUS_ERROR)
    {
        return MLX90393_ERROR;
    }

    // 读取寄存器数据
    if(MLX90393_ReadData(read_data, 3) != MLX90393_OK)
    {
        return MLX90393_ERROR;
    }

    *data = (read_data[1] << 8) | read_data[2];

    return MLX90393_OK;
}

/**
 * @brief  获取状态字节
 * @param  None
 * @retval 状态字节
 */
uint8_t MLX90393_GetStatus(void)
{
    uint8_t status;
    MLX90393_SendCommand(MLX90393_CMD_NOP, &status);
    return status;
}

/**
 * @brief  打印传感器数据
 * @param  data: 数据结构指针
 * @retval None
 */
void MLX90393_PrintData(MLX90393_Data_t *data)
{
    // 计算磁场强度
    float magnitude = sqrtf(data->x * data->x + data->y * data->y + data->z * data->z);

    // 单行显示所有数据
    printf("X:%.2f Y:%.2f Z:%.2f T:%.2f Mag:%.2f\r\n",
           data->x, data->y, data->z, data->temp, magnitude);
}

/**
 * @brief  设置传感器增益
 * @param  gain: 增益值 (MLX90393_GAIN_xxx)
 * @retval MLX90393_Status_t
 */
MLX90393_Status_t MLX90393_SetGain(uint8_t gain)
{
    uint16_t ctrl1_value;

    // 读取当前CTRL1寄存器值
    if(MLX90393_ReadRegister(MLX90393_REG_CTRL1, &ctrl1_value) != MLX90393_OK)
    {
        return MLX90393_ERROR;
    }

    // 清除GAIN_SEL位 (位4-6) 并设置新值
    ctrl1_value &= ~(0x07 << 4);
    ctrl1_value |= (gain & 0x07) << 4;

    // 写入新值
    return MLX90393_WriteRegister(MLX90393_REG_CTRL1, ctrl1_value);
}

/**
 * @brief  设置传感器分辨率
 * @param  resolution: 分辨率值 (MLX90393_RES_xxx)
 * @retval MLX90393_Status_t
 */
MLX90393_Status_t MLX90393_SetResolution(uint8_t resolution)
{
    uint16_t ctrl2_value;

    // 读取当前CTRL2寄存器值
    if(MLX90393_ReadRegister(MLX90393_REG_CTRL2, &ctrl2_value) != MLX90393_OK)
    {
        return MLX90393_ERROR;
    }

    // 清除RES位 (位5-6) 并设置新值
    ctrl2_value &= ~(0x03 << 5);
    ctrl2_value |= (resolution & 0x03) << 5;

    // 写入新值
    return MLX90393_WriteRegister(MLX90393_REG_CTRL2, ctrl2_value);
}

/**
 * @brief  设置传感器滤波器
 * @param  filter: 滤波器值 (MLX90393_FILTER_xxx)
 * @retval MLX90393_Status_t
 */
MLX90393_Status_t MLX90393_SetFilter(uint8_t filter)
{
    uint16_t ctrl2_value;

    // 读取当前CTRL2寄存器值
    if(MLX90393_ReadRegister(MLX90393_REG_CTRL2, &ctrl2_value) != MLX90393_OK)
    {
        return MLX90393_ERROR;
    }

    // 清除DIG_FILT位 (位2-3) 并设置新值
    ctrl2_value &= ~(0x03 << 2);
    ctrl2_value |= (filter & 0x03) << 2;

    // 写入新值
    return MLX90393_WriteRegister(MLX90393_REG_CTRL2, ctrl2_value);
}

/**
 * @brief  初始化滤波器
 * @param  filter: 滤波器结构指针
 * @retval None
 */
void MLX90393_FilterInit(MLX90393_Filter_t *filter)
{
    filter->index = 0;
    filter->filled = 0;

    // 清零缓冲区
    for(uint8_t i = 0; i < 8; i++)
    {
        filter->x_buffer[i] = 0.0f;
        filter->y_buffer[i] = 0.0f;
        filter->z_buffer[i] = 0.0f;
    }
}

/**
 * @brief  对数据进行滤波处理
 * @param  filter: 滤波器结构指针
 * @param  raw_data: 原始数据
 * @param  filtered_data: 滤波后数据
 * @retval None
 */
void MLX90393_FilterData(MLX90393_Filter_t *filter, MLX90393_Data_t *raw_data, MLX90393_Data_t *filtered_data)
{
    // 将新数据存入缓冲区
    filter->x_buffer[filter->index] = raw_data->x;
    filter->y_buffer[filter->index] = raw_data->y;
    filter->z_buffer[filter->index] = raw_data->z;

    // 更新索引
    filter->index = (filter->index + 1) % 8;
    if(!filter->filled && filter->index == 0)
    {
        filter->filled = 1;
    }

    // 计算移动平均值
    float sum_x = 0, sum_y = 0, sum_z = 0;
    uint8_t count = filter->filled ? 8 : filter->index;

    for(uint8_t i = 0; i < count; i++)
    {
        sum_x += filter->x_buffer[i];
        sum_y += filter->y_buffer[i];
        sum_z += filter->z_buffer[i];
    }

    filtered_data->x = sum_x / count;
    filtered_data->y = sum_y / count;
    filtered_data->z = sum_z / count;
    filtered_data->temp = raw_data->temp;  // 温度不滤波
}

/* USER CODE END 0 */
