/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : mlx90393.h
  * @brief          : Header for mlx90393.c file.
  *                   This file contains the MLX90393 sensor driver.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MLX90393_H
#define __MLX90393_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "soft_iic.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

// MLX90393数据结构
typedef struct {
    float x;        // X轴磁场强度 (uT)
    float y;        // Y轴磁场强度 (uT)
    float z;        // Z轴磁场强度 (uT)
    float temp;     // 温度 (°C)
} MLX90393_Data_t;

// MLX90393状态枚举
typedef enum {
    MLX90393_OK = 0,
    MLX90393_ERROR,
    MLX90393_TIMEOUT,
    MLX90393_NOT_FOUND
} MLX90393_Status_t;

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

// MLX90393 I2C地址 (7位地址)
#define MLX90393_I2C_ADDR           0x0C    // 默认I2C地址

// MLX90393命令定义
#define MLX90393_CMD_NOP            0x00    // 空操作
#define MLX90393_CMD_EXIT           0x80    // 退出模式
#define MLX90393_CMD_START_BURST    0x10    // 开始突发模式
#define MLX90393_CMD_WAKE_ON_CHANGE 0x20    // 唤醒变化模式
#define MLX90393_CMD_START_MEASURE  0x30    // 开始测量
#define MLX90393_CMD_READ_MEASURE   0x40    // 读取测量
#define MLX90393_CMD_READ_REG       0x50    // 读取寄存器
#define MLX90393_CMD_WRITE_REG      0x60    // 写入寄存器
#define MLX90393_CMD_MEMORY_RECALL  0xD0    // 内存召回
#define MLX90393_CMD_MEMORY_STORE   0xE0    // 内存存储
#define MLX90393_CMD_RESET          0xF0    // 复位

// 测量轴选择位
#define MLX90393_AXIS_X             0x08    // X轴
#define MLX90393_AXIS_Y             0x04    // Y轴
#define MLX90393_AXIS_Z             0x02    // Z轴
#define MLX90393_AXIS_T             0x01    // 温度
#define MLX90393_AXIS_ALL           (MLX90393_AXIS_X | MLX90393_AXIS_Y | MLX90393_AXIS_Z | MLX90393_AXIS_T)

// 状态字节位定义
#define MLX90393_STATUS_BURST       0x80    // 突发模式状态
#define MLX90393_STATUS_WOC         0x40    // 唤醒变化状态
#define MLX90393_STATUS_SM          0x20    // 单次测量状态
#define MLX90393_STATUS_ERROR       0x10    // 错误状态
#define MLX90393_STATUS_SED         0x08    // 单次错误检测
#define MLX90393_STATUS_RS          0x04    // 复位状态
#define MLX90393_STATUS_D1          0x02    // 数据1就绪
#define MLX90393_STATUS_D0          0x01    // 数据0就绪

// 寄存器地址
#define MLX90393_REG_CTRL1          0x00    // 控制寄存器1
#define MLX90393_REG_CTRL2          0x01    // 控制寄存器2
#define MLX90393_REG_CTRL3          0x02    // 控制寄存器3

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
/* USER CODE BEGIN EFP */

// 基础函数
MLX90393_Status_t MLX90393_Init(void);
MLX90393_Status_t MLX90393_Reset(void);
MLX90393_Status_t MLX90393_CheckConnection(void);

// 数据读取函数
MLX90393_Status_t MLX90393_StartMeasurement(uint8_t axis);
MLX90393_Status_t MLX90393_ReadMeasurement(MLX90393_Data_t *data, uint8_t axis);
MLX90393_Status_t MLX90393_ReadAllData(MLX90393_Data_t *data);

// 寄存器操作函数
MLX90393_Status_t MLX90393_WriteRegister(uint8_t reg_addr, uint16_t data);
MLX90393_Status_t MLX90393_ReadRegister(uint8_t reg_addr, uint16_t *data);

// 工具函数
uint8_t MLX90393_GetStatus(void);
void MLX90393_PrintData(MLX90393_Data_t *data);

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MLX90393_H */
