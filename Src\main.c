/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2023 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under BSD 3-Clause license,
  * the "License"; You may not use this file except in compliance with the
  * License. You may obtain a copy of the License at:
  *                        opensource.org/licenses/BSD-3-Clause
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "mlx90393.h"
#include "soft_iic.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
MLX90393_Data_t sensor_data;
MLX90393_Data_t offset_data = {0, 0, 0, 0};  // 偏移校准数据
MLX90393_Data_t baseline_data = {0, 0, 0, 0}; // 基线数据
uint32_t last_read_time = 0;
uint8_t calibration_done = 0;
uint8_t baseline_set = 0;
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_USART2_UART_Init();
  /* USER CODE BEGIN 2 */

  printf("MLX90393 Magnetic Sensor Test\r\n");
  printf("=============================\r\n");

  // 初始化MLX90393传感器
  MLX90393_Status_t status = MLX90393_Init();
  if(status == MLX90393_OK)
  {
    printf("MLX90393 Init OK!\r\n");
  }
  else if(status == MLX90393_NOT_FOUND)
  {
    printf("MLX90393 Not Found!\r\n");
  }
  else
  {
    printf("MLX90393 Init Failed!\r\n");
  }

  printf("Start reading data...\r\n");
  printf("Calibrating offset (5 seconds)...\r\n\r\n");

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    // LED闪烁指示系统运行
    LED_TOGGLE;

    // 读取MLX90393所有轴数据
    MLX90393_Status_t read_status = MLX90393_ReadAllData(&sensor_data);

    if(read_status == MLX90393_OK)
    {
      // 前5秒进行偏移校准
      if(!calibration_done && HAL_GetTick() < 5000)
      {
        static uint16_t cal_count = 0;
        offset_data.x += sensor_data.x;
        offset_data.y += sensor_data.y;
        offset_data.z += sensor_data.z;
        cal_count++;

        if(HAL_GetTick() >= 4900)  // 接近5秒时完成校准
        {
          offset_data.x /= cal_count;
          offset_data.y /= cal_count;
          offset_data.z /= cal_count;
          calibration_done = 1;
          printf("Calibration done! Offset X:%.1f Y:%.1f Z:%.1f\r\n\r\n",
                 offset_data.x, offset_data.y, offset_data.z);
        }
      }
      else if(calibration_done)
      {
        // 应用偏移校准
        float x_cal = sensor_data.x - offset_data.x;
        float y_cal = sensor_data.y - offset_data.y;
        float z_cal = sensor_data.z - offset_data.z;

        // 计算校准后的磁场强度
        float magnitude = sqrtf(x_cal * x_cal + y_cal * y_cal + z_cal * z_cal);

        // 设置基线（校准完成后的第一次读取）
        if(!baseline_set)
        {
          baseline_data.x = x_cal;
          baseline_data.y = y_cal;
          baseline_data.z = z_cal;
          baseline_set = 1;
          printf("Baseline set. Move magnet to test...\r\n");
        }

        // 计算相对于基线的变化
        float dx = x_cal - baseline_data.x;
        float dy = y_cal - baseline_data.y;
        float dz = z_cal - baseline_data.z;
        float change = sqrtf(dx*dx + dy*dy + dz*dz);

        // 显示数据和变化检测
        printf("Cal X:%.1f Y:%.1f Z:%.1f Mag:%.1f | Change:%.1f",
               x_cal, y_cal, z_cal, magnitude, change);

        // 磁场变化提示
        if(change > 50.0f)
        {
          printf(" [STRONG]");
        }
        else if(change > 20.0f)
        {
          printf(" [MEDIUM]");
        }
        else if(change > 10.0f)
        {
          printf(" [WEAK]");
        }
        printf("\r\n");
      }
    }
    else
    {
      printf("Read Error: %d\r\n", read_status);
    }

    HAL_Delay(200);  // 200ms延时

    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_MSI;
  RCC_OscInitStruct.MSIState = RCC_MSI_ON;
  RCC_OscInitStruct.MSICalibrationValue = 0;
  RCC_OscInitStruct.MSIClockRange = RCC_MSIRANGE_5;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_NONE;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_MSI;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART2;
  PeriphClkInit.Usart2ClockSelection = RCC_USART2CLKSOURCE_PCLK1;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */
#ifdef __GNUC__
#define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
#define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif

PUTCHAR_PROTOTYPE
{
    HAL_UART_Transmit(&huart2, (uint8_t *)&ch, 1, HAL_MAX_DELAY);
    return ch;
}
/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  while(1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
